const express = require('express');
const router = express.Router();
const { authenticateToken } = require('../middleware/authMiddleware');
const allowPaymentAction = require('../middleware/workspaceAuthMiddleware');
const { getBillingStatus, fixCorruptedSubscriptions } = require('../controllers/billingController');
const Subscription = require('../models/Subscription');
const Workspace = require('../models/Workspace');
const Stripe = require('stripe');
const { calculateProratedAmount } = require('../controllers/billingController');

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);

// Get billing status for a workspace
router.get('/workspace/:workspaceId/status', authenticateToken, getBillingStatus);

// Fix corrupted subscription data
router.post('/workspace/:workspaceId/fix-subscription', authenticateToken, fixCorruptedSubscriptions);

// Simple endpoint for payment notices - just returns pending seats count
router.get('/workspace/:workspaceId/pending-seats',
    authenticateToken,
    async (req, res) => {
        try {
            const { workspaceId } = req.params;
            const userId = req.user.id;

            // Find workspace and verify user access
            const workspace = await Workspace.findOne({ shortId: workspaceId })
                .populate('subscription');

            if (!workspace) {
                return res.status(404).json({ message: 'Workspace not found' });
            }

            // Check if user is member of workspace
            const isMember = workspace.ownerId === userId ||
                workspace.members.some(member =>
                    member.user._id.toString() === userId
                ) ||
                workspace.guests.some(guest => guest.toString() === userId);

            if (!isMember) {
                return res.status(403).json({ message: 'Access denied' });
            }

            const subscription = workspace.subscription;

            if (!subscription) {
                return res.json({ pendingSeats: 0, isPro: false });
            }

            const isPro = ['Standard', 'Premium', 'Enterprise'].includes(subscription.planType) &&
                         subscription.status === 'active';

            const totalMembers = workspace.members.length + workspace.guests.length;
            const paidSeats = subscription.reservedSeats || 0;

            res.json({
                pendingSeats: subscription.pendingSeats || 0,
                paidSeats,
                totalMembers,
                unpaidMembers: Math.max(0, totalMembers - paidSeats),
                isPro
            });

        } catch (error) {
            console.error('Error getting pending seats:', error);
            res.status(500).json({ message: 'Internal server error' });
        }
    }
);

// Pay for pending seats
router.post('/workspace/:workspaceId/pay-pending-seats',
    authenticateToken,
    async (req, res) => {
        try {
            const { workspaceId } = req.params;
            const userId = req.user.id;

            // Find workspace and verify admin access
            const workspace = await Workspace.findOne({ shortId: workspaceId })
                .populate('subscription');

            if (!workspace) {
                return res.status(404).json({ message: 'Workspace not found' });
            }

            const isAdmin = workspace.ownerId === userId ||
                workspace.members.some(member =>
                    member.user._id.toString() === userId && member.role === 'admin'
                );

            if (!isAdmin) {
                return res.status(403).json({ message: 'Only admins can pay for seats' });
            }

            const subscription = workspace.subscription;

            if (!subscription || subscription.pendingSeats <= 0) {
                return res.status(400).json({ message: 'No pending seats to pay for' });
            }

            // Calculate prorated amount
            const proratedData = calculateProratedAmount(
                subscription.planType,
                subscription.billingCycle,
                subscription.pendingSeats,
                subscription.currentPeriodStart,
                subscription.currentPeriodEnd
            );

            // Create payment intent for pending seats
            const paymentIntent = await stripe.paymentIntents.create({
                amount: proratedData.proratedAmount,
                currency: 'usd',
                customer: subscription.stripeCustomerId,
                metadata: {
                    workspaceId: workspace._id.toString(),
                    userId,
                    planType: subscription.planType,
                    billingCycle: subscription.billingCycle,
                    pendingSeats: subscription.pendingSeats,
                    type: 'pending_seats_payment'
                },
                payment_method_types: ['card'],
                setup_future_usage: 'off_session'
            });

            res.json({
                clientSecret: paymentIntent.client_secret,
                amount: proratedData.proratedAmount,
                amountDollars: proratedData.proratedAmountDollars,
                seats: subscription.pendingSeats,
                proratedData
            });

        } catch (error) {
            console.error('Error creating payment for pending seats:', error);
            res.status(500).json({ message: 'Internal server error' });
        }
    }
);

// Debug endpoint to clean up duplicate subscriptions
router.post('/cleanup-subscriptions',
    authenticateToken,
    async (req, res) => {
        try {
            const userId = req.user.id;

            // Find all workspaces where user is admin/owner
            const workspaces = await Workspace.find({
                $or: [
                    { ownerId: userId },
                    { 'members.user': userId, 'members.role': 'admin' }
                ]
            });

            let cleanedCount = 0;
            const results = [];

            for (const workspace of workspaces) {
                // Find all subscriptions for this workspace
                const subscriptions = await Subscription.find({ workspaceId: workspace._id })
                    .sort({ createdAt: -1 });

                if (subscriptions.length > 1) {
                    // Keep the newest subscription, delete the rest
                    const keepSubscription = subscriptions[0];
                    const deleteSubscriptions = subscriptions.slice(1);

                    // Update workspace to reference the correct subscription
                    await Workspace.findByIdAndUpdate(workspace._id, {
                        subscription: keepSubscription._id
                    });

                    // Delete old subscriptions
                    for (const oldSub of deleteSubscriptions) {
                        await Subscription.findByIdAndDelete(oldSub._id);
                        cleanedCount++;
                    }

                    results.push({
                        workspaceId: workspace.shortId,
                        workspaceName: workspace.name,
                        kept: keepSubscription._id,
                        deleted: deleteSubscriptions.map(s => s._id),
                        deletedCount: deleteSubscriptions.length
                    });
                }
            }

            res.json({
                message: `Cleaned up ${cleanedCount} duplicate subscriptions`,
                cleanedCount,
                results
            });

        } catch (error) {
            console.error('Error cleaning subscriptions:', error);
            res.status(500).json({ message: 'Internal server error' });
        }
    }
);

// Debug endpoint to reset ALL subscriptions (for testing)
router.post('/reset-all-subscriptions',
    authenticateToken,
    async (req, res) => {
        try {
            const userId = req.user.id;

            // Find all workspaces where user is admin/owner
            const workspaces = await Workspace.find({
                $or: [
                    { ownerId: userId },
                    { 'members.user': userId, 'members.role': 'admin' }
                ]
            }).populate('subscription');

            console.log(`Found ${workspaces.length} workspaces for user ${userId}`);

            let resetCount = 0;
            const results = [];

            for (const workspace of workspaces) {
                if (workspace.subscription) {
                    const currentMemberCount = workspace.members.length + workspace.guests.length;

                    // Debug logging
                    console.log(`Reset Debug for ${workspace.name}:`, {
                        workspaceId: workspace.shortId,
                        members: workspace.members.length,
                        guests: workspace.guests.length,
                        currentMemberCount,
                        previousReservedSeats: workspace.subscription.reservedSeats
                    });

                    // Reset subscription seats to 0 - force fresh start
                    // This ensures everything shows as unpaid and needs payment
                    workspace.subscription.reservedSeats = 0;
                    workspace.subscription.pendingSeats = 0;
                    workspace.subscription.totalSeats = 0;
                    workspace.subscription.quantity = 0;
                    workspace.subscription.seatHistory = [{
                        action: 'removed',
                        seats: 0,
                        userId,
                        reason: `Bulk reset: Set all seats to 0 - fresh start required`
                    }];

                    await workspace.subscription.save();
                    resetCount++;

                    results.push({
                        workspaceId: workspace.shortId,
                        workspaceName: workspace.name,
                        totalMembers: currentMemberCount,
                        paidSeats: 0,
                        previousReservedSeats: workspace.subscription.reservedSeats
                    });
                }
            }

            res.json({
                message: `Successfully reset ${resetCount} subscriptions`,
                resetCount,
                results
            });

        } catch (error) {
            console.error('Error resetting all subscriptions:', error);
            res.status(500).json({ message: 'Internal server error' });
        }
    }
);

// Debug endpoint to reset subscription seats (for testing)
router.post('/workspace/:workspaceId/reset-seats',
    authenticateToken,
    async (req, res) => {
        try {
            const { workspaceId } = req.params;
            const userId = req.user.id;

            // Find workspace and verify admin access
            const workspace = await Workspace.findOne({ shortId: workspaceId })
                .populate('subscription');

            if (!workspace) {
                return res.status(404).json({ message: 'Workspace not found' });
            }

            const isAdmin = workspace.ownerId === userId ||
                workspace.members.some(member =>
                    member.user._id.toString() === userId && member.role === 'admin'
                );

            if (!isAdmin) {
                return res.status(403).json({ message: 'Only admins can reset seats' });
            }

            const subscription = workspace.subscription;

            if (!subscription) {
                return res.status(400).json({ message: 'No subscription found' });
            }

            // Reset seats to match current member count
            const currentMemberCount = workspace.members.length + workspace.guests.length;
            subscription.reservedSeats = currentMemberCount; // Set paid seats to current member count
            subscription.pendingSeats = 0; // Clear any pending seats
            subscription.totalSeats = currentMemberCount;
            subscription.seatHistory = [{
                action: 'paid',
                seats: currentMemberCount,
                userId,
                reason: `Admin reset: Set paid seats to ${currentMemberCount} (current member count)`
            }];

            await subscription.save();

            res.json({
                message: 'Seats reset successfully',
                paidSeats: subscription.reservedSeats,
                pendingSeats: subscription.pendingSeats,
                totalMembers: currentMemberCount,
                unpaidMembers: 0 // Should be 0 after reset
            });

        } catch (error) {
            console.error('Error resetting seats:', error);
            res.status(500).json({ message: 'Internal server error' });
        }
    }
);

// Increase reserved seats (for future members)
router.post('/workspace/:workspaceId/increase-seats',
    authenticateToken,
    async (req, res) => {
        try {
            const { workspaceId } = req.params;
            const { additionalSeats } = req.body;
            const userId = req.user.id;

            if (!additionalSeats || additionalSeats <= 0) {
                return res.status(400).json({ message: 'Additional seats must be a positive number' });
            }

            // Find workspace and verify admin access
            const workspace = await Workspace.findOne({ shortId: workspaceId })
                .populate('subscription');

            if (!workspace) {
                return res.status(404).json({ message: 'Workspace not found' });
            }

            const isAdmin = workspace.ownerId === userId ||
                workspace.members.some(member =>
                    member.user._id.toString() === userId && member.role === 'admin'
                );

            if (!isAdmin) {
                return res.status(403).json({ message: 'Only admins can increase seats' });
            }

            const subscription = workspace.subscription;

            if (!subscription) {
                return res.status(400).json({ message: 'No active subscription found' });
            }

            // Calculate prorated amount for additional seats
            const proratedData = calculateProratedAmount(
                subscription.planType,
                subscription.billingCycle,
                additionalSeats,
                subscription.currentPeriodStart,
                subscription.currentPeriodEnd
            );

            // Create payment intent for additional seats
            const paymentIntent = await stripe.paymentIntents.create({
                amount: proratedData.proratedAmount,
                currency: 'usd',
                customer: subscription.stripeCustomerId,
                metadata: {
                    workspaceId: workspace._id.toString(),
                    userId,
                    planType: subscription.planType,
                    billingCycle: subscription.billingCycle,
                    additionalSeats: additionalSeats,
                    type: 'additional_seats_payment'
                },
                payment_method_types: ['card'],
                setup_future_usage: 'off_session'
            });

            res.json({
                clientSecret: paymentIntent.client_secret,
                amount: proratedData.proratedAmount,
                amountDollars: proratedData.proratedAmountDollars,
                seats: additionalSeats,
                proratedData
            });

        } catch (error) {
            console.error('Error creating payment for additional seats:', error);
            res.status(500).json({ message: 'Internal server error' });
        }
    }
);

module.exports = router;
